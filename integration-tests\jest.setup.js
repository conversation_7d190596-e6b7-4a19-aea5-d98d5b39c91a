require('dotenv').config();

// Global test configuration
global.testConfig = {
  authServiceUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  assessmentServiceUrl: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003',
  archiveServiceUrl: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
  apiGatewayUrl: process.env.API_GATEWAY_URL || 'http://localhost:3000',
  notificationServiceUrl: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005',
  testTimeout: parseInt(process.env.TEST_TIMEOUT) || 30000,
  waitForProcessing: parseInt(process.env.WAIT_FOR_PROCESSING) || 120000,
  testEmail: process.env.TEST_EMAIL || '<EMAIL>',
  testPassword: process.env.TEST_PASSWORD || 'password123',
  adminUsername: process.env.ADMIN_USERNAME || 'admin',
  adminPassword: process.env.ADMIN_PASSWORD || 'admin123',
  serviceApiKey: process.env.SERVICE_API_KEY || 'internal_service_secret_key_change_in_production'
};

// Global test utilities
global.sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Generate unique test email for each test run
global.generateTestEmail = () => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `test-${timestamp}-${random}@example.com`;
};

console.log('Jest setup completed with configuration:', {
  authServiceUrl: global.testConfig.authServiceUrl,
  assessmentServiceUrl: global.testConfig.assessmentServiceUrl,
  archiveServiceUrl: global.testConfig.archiveServiceUrl,
  apiGatewayUrl: global.testConfig.apiGatewayUrl,
  notificationServiceUrl: global.testConfig.notificationServiceUrl
});
