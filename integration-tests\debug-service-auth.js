#!/usr/bin/env node

/**
 * Debug Service Authentication
 * Script untuk debug masalah authentication antar services
 */

require('dotenv').config();
const axios = require('axios');

// Configuration
const config = {
  assessmentServiceUrl: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003',
  notificationServiceUrl: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005',
  archiveServiceUrl: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
  serviceKey: process.env.SERVICE_API_KEY || 'internal_service_secret_key_change_in_production'
};

console.log('🔧 ATMA Service Authentication Debug Tool');
console.log('=' .repeat(60));
console.log('Configuration:');
console.log(`  Service Key: ${config.serviceKey}`);
console.log(`  Assessment Service: ${config.assessmentServiceUrl}`);
console.log(`  Notification Service: ${config.notificationServiceUrl}`);
console.log(`  Archive Service: ${config.archiveServiceUrl}`);
console.log('=' .repeat(60));

async function testServiceAuth(serviceName, url, endpoint, payload = {}) {
  console.log(`\n🧪 Testing ${serviceName} Authentication`);
  console.log(`URL: ${url}${endpoint}`);
  
  const headers = {
    'X-Internal-Service': 'true',
    'X-Service-Key': config.serviceKey,
    'Content-Type': 'application/json'
  };
  
  console.log('Headers:', JSON.stringify(headers, null, 2));
  
  try {
    const response = await axios.post(`${url}${endpoint}`, payload, {
      headers,
      timeout: 10000
    });
    
    console.log(`✅ ${serviceName}: Authentication successful`);
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(response.data, null, 2));
    return true;
    
  } catch (error) {
    console.log(`❌ ${serviceName}: Authentication failed`);
    
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response:`, JSON.stringify(error.response.data, null, 2));
    } else {
      console.log(`Error: ${error.message}`);
    }
    return false;
  }
}

async function testAssessmentServiceCallback() {
  const payload = {
    jobId: 'test-job-id-12345',
    resultId: 'test-result-id-67890',
    status: 'completed'
  };
  
  return await testServiceAuth(
    'Assessment Service Callback',
    config.assessmentServiceUrl,
    '/assessments/callback/completed',
    payload
  );
}

async function testNotificationService() {
  const payload = {
    userId: '123e4567-e89b-12d3-a456-426614174000',
    jobId: '123e4567-e89b-12d3-a456-426614174001',
    resultId: '123e4567-e89b-12d3-a456-426614174002',
    status: 'completed',
    message: 'Test notification'
  };

  return await testServiceAuth(
    'Notification Service',
    config.notificationServiceUrl,
    '/notifications/analysis-complete',
    payload
  );
}

async function testArchiveService() {
  const payload = {
    user_id: '123e4567-e89b-12d3-a456-426614174000',
    assessment_data: {
      riasec: { realistic: 50, investigative: 60, artistic: 70, social: 40, enterprising: 30, conventional: 20 },
      ocean: { openness: 80, conscientiousness: 70, extraversion: 60, agreeableness: 50, neuroticism: 40 },
      viaIs: { creativity: 85, curiosity: 75, judgment: 65 }
    },
    persona_profile: {
      archetype: 'Test Archetype',
      description: 'Test description',
      shortSummary: 'Test short summary',
      strengths: ['Test strength 1', 'Test strength 2', 'Test strength 3'],
      weaknesses: ['Test weakness 1', 'Test weakness 2', 'Test weakness 3'],
      challenges: ['Test challenge'],
      careerSuggestions: ['Test career'],
      careerRecommendation: ['Test career recommendation 1', 'Test career recommendation 2'],
      developmentAreas: ['Test development'],
      insights: ['Test insight 1', 'Test insight 2'],
      workEnvironment: 'Test work environment',
      roleModel: ['Test role model 1', 'Test role model 2']
    },
    status: 'completed'
  };

  return await testServiceAuth(
    'Archive Service',
    config.archiveServiceUrl,
    '/archive/results',
    payload
  );
}

async function checkServiceHealth() {
  console.log(`\n🏥 Checking Service Health`);
  
  const services = [
    { name: 'Assessment Service', url: `${config.assessmentServiceUrl}/health/live` },
    { name: 'Notification Service', url: `${config.notificationServiceUrl}/health` },
    { name: 'Archive Service', url: `${config.archiveServiceUrl}/health` }
  ];
  
  for (const service of services) {
    try {
      const response = await axios.get(service.url, { timeout: 5000 });
      console.log(`✅ ${service.name}: Healthy (${response.status})`);
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${service.name}: Service not running`);
      } else {
        console.log(`❌ ${service.name}: ${error.message}`);
      }
    }
  }
}

async function main() {
  try {
    // Check service health first
    await checkServiceHealth();
    
    console.log(`\n🔐 Testing Service Authentication`);
    console.log('─'.repeat(60));
    
    // Test each service authentication
    const results = {
      assessment: await testAssessmentServiceCallback(),
      notification: await testNotificationService(),
      archive: await testArchiveService()
    };
    
    // Summary
    console.log(`\n📊 Authentication Test Summary`);
    console.log('=' .repeat(60));
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    Object.entries(results).forEach(([service, success]) => {
      const status = success ? '✅ PASS' : '❌ FAIL';
      console.log(`  ${service.padEnd(20)}: ${status}`);
    });
    
    console.log('─'.repeat(60));
    console.log(`Result: ${successCount}/${totalTests} services authenticated successfully`);
    
    if (successCount === totalTests) {
      console.log(`🎉 All service authentications working correctly!`);
      process.exit(0);
    } else {
      console.log(`⚠️ Some service authentications failed. Check configuration.`);
      console.log(`\n💡 Troubleshooting tips:`);
      console.log(`  1. Ensure all services are running`);
      console.log(`  2. Check SERVICE_API_KEY in .env file`);
      console.log(`  3. Verify service keys match across all services`);
      console.log(`  4. Check service endpoints are correct`);
      process.exit(1);
    }
    
  } catch (error) {
    console.error(`💥 Debug script failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n⚠️ Debug script interrupted by user');
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = { main };
