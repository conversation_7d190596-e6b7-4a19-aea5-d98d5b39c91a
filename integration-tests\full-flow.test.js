const TestHelpers = require('./utils/test-helpers');

describe('ATMA Full Flow Integration Test', () => {
  let testHelpers;
  let testUser;
  let authToken;
  let assessmentJobId;
  let analysisResult;

  beforeAll(async () => {
    testHelpers = new TestHelpers();
    console.log('🚀 Starting ATMA Full Flow Integration Test');

    // Check services health before starting tests
    console.log('🏥 Checking services health...');
    try {
      const healthResults = await testHelpers.checkAllServicesHealth();
      const healthyServices = Object.entries(healthResults).filter(([name, result]) => result.status === 'healthy');
      const unhealthyServices = Object.entries(healthResults).filter(([name, result]) => result.status === 'unhealthy');

      console.log(`✅ Healthy services: ${healthyServices.length}/4`);
      if (unhealthyServices.length > 0) {
        console.log(`⚠️ Unhealthy services: ${unhealthyServices.map(([name]) => name).join(', ')}`);
      }
    } catch (error) {
      console.log(`⚠️ Health check failed: ${error.message}`);
    }
  });

  afterAll(async () => {
    if (testUser && testUser.response.data.user.id) {
      await testHelpers.cleanup(testUser.response.data.user.id);
    }
    console.log('🏁 ATMA Full Flow Integration Test completed');
  });

  describe('1. User Registration', () => {
    it('should register a new user successfully', async () => {
      testUser = await testHelpers.registerUser();
      
      expect(testUser.response).toHaveProperty('success', true);
      expect(testUser.response).toHaveProperty('message', 'User registered successfully');
      expect(testUser.response.data).toHaveProperty('user');
      expect(testUser.response.data.user).toHaveProperty('id');
      expect(testUser.response.data.user).toHaveProperty('email', testUser.email);
      expect(testUser.response.data.user).toHaveProperty('tokenBalance');
      
      console.log(`✅ User registered: ${testUser.email}`);
      console.log(`👤 User ID: ${testUser.response.data.user.id}`);
      console.log(`🪙 Token Balance: ${testUser.response.data.user.tokenBalance}`);
    });
  });

  describe('2. User Login', () => {
    it('should login user and receive JWT token', async () => {
      const loginResult = await testHelpers.loginUser(testUser.email, testUser.password);
      authToken = loginResult.token;
      
      expect(loginResult.response).toHaveProperty('success', true);
      expect(loginResult.response).toHaveProperty('message', 'Login successful');
      expect(loginResult.response.data).toHaveProperty('token');
      expect(loginResult.response.data).toHaveProperty('user');
      expect(loginResult.response.data.user).toHaveProperty('id', testUser.response.data.user.id);
      
      console.log(`✅ User logged in successfully`);
      console.log(`🔑 JWT Token received: ${authToken.substring(0, 20)}...`);
    });
  });

  describe('3. Assessment Submission', () => {
    it('should submit assessment data successfully', async () => {
      const assessmentData = testHelpers.loadSampleAssessment();
      const submitResult = await testHelpers.submitAssessment(authToken, assessmentData);
      assessmentJobId = submitResult.data.jobId;
      
      expect(submitResult).toHaveProperty('success', true);
      expect(submitResult).toHaveProperty('message', 'Assessment submitted successfully and queued for analysis');
      expect(submitResult.data).toHaveProperty('jobId');
      expect(submitResult.data).toHaveProperty('status', 'queued');
      expect(submitResult.data).toHaveProperty('estimatedProcessingTime');
      
      console.log(`✅ Assessment submitted successfully`);
      console.log(`🆔 Job ID: ${assessmentJobId}`);
      console.log(`⏱️ Estimated processing time: ${submitResult.data.estimatedProcessingTime}`);
    });
  });

  describe('4. Assessment Processing', () => {
    it('should process assessment and complete successfully', async () => {
      const completionResult = await testHelpers.waitForAssessmentCompletion(
        authToken, 
        assessmentJobId, 
        global.testConfig.waitForProcessing
      );
      
      expect(completionResult.data).toHaveProperty('status', 'completed');
      expect(completionResult.data).toHaveProperty('result');
      
      console.log(`✅ Assessment processing completed`);
      console.log(`📊 Processing result available`);
    }, global.testConfig.waitForProcessing + 10000); // Add extra buffer time
  });

  describe('5. Archive Service Verification', () => {
    it('should retrieve analysis results from archive service', async () => {
      // Wait a bit for the result to be saved to archive
      await global.sleep(5000);
      
      const archiveResults = await testHelpers.getAnalysisResults(authToken);
      
      expect(archiveResults).toHaveProperty('success', true);
      expect(archiveResults.data).toBeInstanceOf(Array);
      expect(archiveResults.data.length).toBeGreaterThan(0);
      
      // Find our specific result
      analysisResult = archiveResults.data.find(result => 
        result.user_id === testUser.response.data.user.id
      );
      
      expect(analysisResult).toBeDefined();
      testHelpers.verifyAssessmentResult(analysisResult);
      
      console.log(`✅ Analysis result found in archive service`);
      console.log(`📋 Result ID: ${analysisResult.id}`);
    });

    it('should have correct assessment data in archive', async () => {
      expect(analysisResult.assessment_data).toBeDefined();
      expect(analysisResult.assessment_data).toHaveProperty('riasec');
      expect(analysisResult.assessment_data).toHaveProperty('ocean');
      expect(analysisResult.assessment_data).toHaveProperty('viaIs');
      
      // Verify some specific values from our test data
      expect(analysisResult.assessment_data.riasec).toHaveProperty('investigative', 85);
      expect(analysisResult.assessment_data.ocean).toHaveProperty('openness', 80);
      expect(analysisResult.assessment_data.viaIs).toHaveProperty('creativity', 85);
      
      console.log(`✅ Assessment data correctly stored in archive`);
    });

    it('should have valid persona profile in archive', async () => {
      const personaProfile = analysisResult.persona_profile;
      
      expect(personaProfile).toHaveProperty('archetype');
      expect(personaProfile).toHaveProperty('description');
      expect(personaProfile).toHaveProperty('strengths');
      expect(personaProfile).toHaveProperty('challenges');
      expect(personaProfile).toHaveProperty('careerSuggestions');
      expect(personaProfile).toHaveProperty('developmentAreas');
      
      // Verify arrays are not empty
      expect(personaProfile.strengths).toBeInstanceOf(Array);
      expect(personaProfile.strengths.length).toBeGreaterThan(0);
      expect(personaProfile.challenges).toBeInstanceOf(Array);
      expect(personaProfile.challenges.length).toBeGreaterThan(0);
      expect(personaProfile.careerSuggestions).toBeInstanceOf(Array);
      expect(personaProfile.careerSuggestions.length).toBeGreaterThan(0);
      
      console.log(`✅ Persona profile correctly generated and stored`);
      console.log(`🎭 Archetype: ${personaProfile.archetype}`);
      console.log(`💪 Strengths count: ${personaProfile.strengths.length}`);
      console.log(`🎯 Career suggestions count: ${personaProfile.careerSuggestions.length}`);
    });
  });

  describe('6. Notification Service Verification', () => {
    it('should have notification service operational', async () => {
      try {
        const notificationStatus = await testHelpers.checkNotificationService();

        expect(notificationStatus).toHaveProperty('success', true);
        expect(notificationStatus.data).toHaveProperty('status', 'operational');
        expect(notificationStatus.data).toHaveProperty('connections');

        console.log(`✅ Notification service is operational`);
      } catch (error) {
        console.log(`⚠️ Notification service check failed: ${error.message}`);
        // Don't fail the test if notification service is down
        // as it's not critical for the core flow
      }
    });
  });

  describe('7. End-to-End Verification', () => {
    it('should complete full flow from registration to archive storage', async () => {
      // Verify the complete flow
      expect(testUser).toBeDefined();
      expect(authToken).toBeDefined();
      expect(assessmentJobId).toBeDefined();
      expect(analysisResult).toBeDefined();

      // Verify data consistency
      expect(analysisResult.user_id).toBe(testUser.response.data.user.id);
      expect(analysisResult.status).toBe('completed');

      console.log(`🎉 Full flow completed successfully!`);
      console.log(`📊 Summary:`);
      console.log(`   👤 User: ${testUser.email}`);
      console.log(`   🆔 User ID: ${testUser.response.data.user.id}`);
      console.log(`   🔑 Job ID: ${assessmentJobId}`);
      console.log(`   📋 Result ID: ${analysisResult.id}`);
      console.log(`   🎭 Persona: ${analysisResult.persona_profile.archetype}`);
      console.log(`   📅 Created: ${analysisResult.created_at}`);
    });

    it('should verify complete integration chain', async () => {
      // Verify the complete integration chain worked
      console.log(`🔗 Verifying complete integration chain:`);
      console.log(`   1. ✅ User registered in Auth Service`);
      console.log(`   2. ✅ Assessment submitted to Assessment Service`);
      console.log(`   3. ✅ Job processed by Analysis Worker`);
      console.log(`   4. ✅ Result saved to Archive Service`);
      console.log(`   5. ✅ Notification sent (if service available)`);

      // Final verification that all data is consistent
      expect(analysisResult.user_id).toBe(testUser.response.data.user.id);
      expect(analysisResult.assessment_data).toBeDefined();
      expect(analysisResult.persona_profile).toBeDefined();
      expect(analysisResult.status).toBe('completed');

      console.log(`🎯 Integration chain verification completed successfully!`);
    });
  });
});
