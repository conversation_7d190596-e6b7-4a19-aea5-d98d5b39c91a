const express = require('express');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const { authenticateToken, requireTokenBalance } = require('../middleware/auth');
const { validateSchema } = require('../middleware/validation');
const { assessmentSchema } = require('../schemas/assessment');
const queueService = require('../services/queueService');
const authService = require('../services/authService');
const jobTracker = require('../jobs/jobTracker');
const logger = require('../utils/logger');
const { sendSuccess, sendError, sendNotFound } = require('../utils/responseHelper');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

/**
 * @route POST /assessments/callback/completed
 * @description Callback endpoint for analysis worker to update job status
 * @access Internal Service Only
 */
router.post('/callback/completed', async(req, res, next) => {
  try {
    // Validate internal service authentication
    const serviceKey = req.headers['x-service-key'];
    const expectedKey = process.env.INTERNAL_SERVICE_KEY;

    if (!serviceKey || serviceKey !== expectedKey) {
      return sendError(res, 'UNAUTHORIZED', 'Invalid service key', {}, 401);
    }

    const { jobId, resultId, status } = req.body;

    // Validate required fields
    if (!jobId || !resultId || !status) {
      return sendError(res, 'VALIDATION_ERROR', 'Missing required fields', {
        jobId: 'Job ID is required',
        resultId: 'Result ID is required',
        status: 'Status is required'
      }, 400);
    }

    // Update job status
    const updatedJob = jobTracker.updateJobStatus(jobId, status, 100);

    if (!updatedJob) {
      return sendNotFound(res, 'Job not found');
    }

    logger.info('Job status updated via callback', {
      jobId,
      resultId,
      status,
      userId: updatedJob.userId
    });

    return sendSuccess(res, 'Job status updated successfully', {
      jobId,
      status: updatedJob.status,
      progress: updatedJob.progress,
      updatedAt: updatedJob.updatedAt
    });
  } catch (error) {
    next(error);
  }
});

// All other assessment routes require authentication
router.use(authenticateToken);

/**
 * @route POST /assessments/submit
 * @description Submit assessment data for AI analysis
 * @access Private
 */
router.post('/submit', validateSchema(assessmentSchema), requireTokenBalance(1), async(req, res, next) => {
  try {
    const { id: userId, email: userEmail } = req.user;
    const assessmentData = req.body;
    const tokenCost = parseInt(process.env.ANALYSIS_TOKEN_COST || '1');

    logger.info('Assessment submission received', {
      userId,
      userEmail,
      assessmentTypes: Object.keys(assessmentData),
      ip: req.ip
    });

    // Deduct tokens from user balance
    try {
      const updatedUser = await authService.deductTokens(userId, req.token, tokenCost);
      req.user.tokenBalance = updatedUser.token_balance;
    } catch (error) {
      if (error instanceof AppError && error.code === 'INSUFFICIENT_TOKENS') {
        return sendError(res, 'INSUFFICIENT_TOKENS', error.message, error.details, 402);
      }
      throw error;
    }

    // Generate job ID and create job
    const jobId = uuidv4();

    // Create job in tracker
    jobTracker.createJob(jobId, userId, userEmail, assessmentData);

    // Publish job to queue with the same jobId
    await queueService.publishAssessmentJob(assessmentData, userId, userEmail, jobId);

    // Get queue position
    const queueStats = await queueService.getQueueStats();

    // Return success response
    return sendSuccess(res, 'Assessment submitted successfully and queued for analysis', {
      jobId,
      status: jobTracker.JOB_STATUS.QUEUED,
      estimatedProcessingTime: '2-5 minutes',
      queuePosition: queueStats.messageCount,
      tokenCost,
      remainingTokens: req.user.tokenBalance
    }, 202);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /assessments/status/:jobId
 * @description Check assessment processing status
 * @access Private
 */
router.get('/status/:jobId', async(req, res, next) => {
  try {
    const { jobId } = req.params;
    const { id: userId } = req.user;

    // Validate jobId format
    const jobIdSchema = Joi.string().uuid().required();
    const { error } = jobIdSchema.validate(jobId);

    if (error) {
      return sendError(res, 'VALIDATION_ERROR', 'Invalid job ID format', {
        jobId: 'Job ID must be a valid UUID'
      }, 400);
    }

    // Get job from tracker
    const job = jobTracker.getJob(jobId);

    if (!job) {
      return sendNotFound(res, 'Job not found');
    }

    // Check if user owns the job
    if (!jobTracker.isJobOwner(jobId, userId)) {
      return sendError(res, 'FORBIDDEN', 'You do not have access to this job', {}, 403);
    }

    // Return job status
    return sendSuccess(res, 'Job status retrieved successfully', {
      jobId: job.jobId,
      status: job.status,
      progress: job.progress,
      estimatedTimeRemaining: job.estimatedProcessingTime,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt
    });
  } catch (error) {
    next(error);
  }
});



/**
 * @route GET /assessments/queue/status
 * @description Get queue status (for monitoring)
 * @access Private
 */
router.get('/queue/status', async(req, res, next) => {
  try {
    // Get queue statistics
    const queueStats = await queueService.getQueueStats();

    // Get job statistics
    const jobStats = jobTracker.getJobStats();

    // Calculate average processing time (mock data for now)
    const averageProcessingTime = '3.2 minutes';

    // Calculate estimated wait time based on queue length
    let estimatedWaitTime = 'Less than a minute';
    if (queueStats.messageCount > 10) {
      estimatedWaitTime = '10+ minutes';
    } else if (queueStats.messageCount > 5) {
      estimatedWaitTime = '5-10 minutes';
    } else if (queueStats.messageCount > 0) {
      estimatedWaitTime = '1-5 minutes';
    }

    return sendSuccess(res, 'Queue status retrieved successfully', {
      queueLength: queueStats.messageCount,
      activeWorkers: queueStats.consumerCount,
      averageProcessingTime,
      estimatedWaitTime,
      jobStats
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
